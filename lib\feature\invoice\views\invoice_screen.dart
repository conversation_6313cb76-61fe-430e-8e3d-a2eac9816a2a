import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mk2_dashboard/core/my_colors.dart';
import 'package:mk2_dashboard/feature/home/<USER>/container_home.dart';
import 'package:mk2_dashboard/feature/invoice/views/all_invoices_screen.dart';

import '../../../core/spacer.dart';
import 'mounth_invoice_Screen.dart';

class InvoiceScreen extends StatelessWidget {
  const InvoiceScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      appBar: AppBar(
        leading: IconButton(
          onPressed: () {
            Navigator.pop(context);
          },
          icon: const Icon(Icons.arrow_back, color: Colors.white),
        ),
        backgroundColor: MyColors.darkColor,
        title: const Text(
          "الفواتير",
          style: TextStyle(
            color: Colors.white,
            fontSize: 20,
            fontWeight: FontWeight.bold,
          ),
        ),
        centerTitle: true,
      ),
      body: SizedBox(
        width: double.infinity,
        child: Column(
          children: [
            verticalSpacer(height: 40),
            Expanded(
              child: Padding(
                padding: const EdgeInsets.symmetric(horizontal: 10),
                child: Center(
                  child: GridView.count(
                    crossAxisCount: 2,
                    crossAxisSpacing: 10,
                    mainAxisSpacing: 30,
                    children: [
                      ContainerHome(
                        icon: Icons.receipt_long_sharp,
                        text: 'جميع الفواتير',
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (ctx) => const AllInvoicesScreen(),
                            ),
                          );
                        },
                      ),
                      ContainerHome(
                        icon: Icons.receipt_long,
                        text: 'فواتير الشهر',
                        onTap: () {
                          Navigator.push(
                            context,
                            MaterialPageRoute(
                              builder: (ctx) => const MounthInvoiceScreen(),
                            ),
                          );
                        },
                      ),
                    ],
                  ),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}
