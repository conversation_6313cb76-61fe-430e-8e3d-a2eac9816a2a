import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';

import '../../../../core/my_colors.dart';
import '../../../../core/my_styles.dart';
import '../../../../core/spacer.dart';

class HeaderChickenNum extends StatelessWidget {
  final String chickenTotal;
  final void Function() onAddChicken;
  final void Function() onMinusChicken;
  const HeaderChickenNum({
    super.key,
    required this.chickenTotal,
    required this.onAddChicken,
    required this.onMinusChicken,
  });

  @override
  Widget build(BuildContext context) {
    return Stack(
      alignment: Alignment.bottomCenter,
      children: [
        Column(
          children: [
            Container(
              width: double.infinity,
              height: 150,
              color: MyColors.darkColor,
              child: Padding(
                padding: const EdgeInsets.only(bottom: 40),
                child: Center(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        chickenTotal,
                        style: MyStyles.theMainTitle.copyWith(
                          fontSize: 40,
                          fontWeight: FontWeight.bold,
                          color: Colors.white,
                        ),
                      ),
                      horizontalSpacer(width: 5),
                      const Icon(
                        Icons.favorite,
                        color: Colors.white,
                        size: 40,
                      ),
                    ],
                  ),
                ),
              ),
            ),
            Container(
              width: double.infinity,
              height: 70,
              color: Colors.white,
            ),
          ],
        ),
        Padding(
          padding: const EdgeInsets.only(left: 20, right: 20, bottom: 30),
          child: Container(
            width: double.infinity,
            height: 75,
            decoration: BoxDecoration(
                color: Colors.white,
                borderRadius: BorderRadius.circular(10),
                boxShadow: List.generate(
                  1,
                  (index) => BoxShadow(
                    color: Colors.grey.withOpacity(0.4),
                    spreadRadius: 5,
                    blurRadius: 7,
                    offset: const Offset(0, 3),
                  ),
                )),
            child: Row(
              // crossAxisAlignment: CrossAxisAlignment.end,
              //  mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Expanded(
                  child: IconButton(
                    onPressed: onMinusChicken,
                    icon: Column(
                      children: [
                        const Icon(
                          CupertinoIcons.heart_slash,
                          size: 35,
                          color: MyColors.darkColor,
                        ),
                        Text(
                          'اضافه نافق',
                          style: MyStyles.theMainTitleField.copyWith(
                            fontSize: 14,
                          ),
                        )
                      ],
                    ),
                  ),
                ),
                Expanded(
                  child: IconButton(
                    onPressed: onAddChicken,
                    icon: Column(
                      children: [
                        const Icon(
                          Icons.favorite_border,
                          size: 35,
                          color: MyColors.darkColor,
                        ),
                        Text(
                          'اضافه دجاج',
                          style: MyStyles.theMainTitleField.copyWith(
                            fontSize: 14,
                          ),
                        )
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ],
    );
  }
}
