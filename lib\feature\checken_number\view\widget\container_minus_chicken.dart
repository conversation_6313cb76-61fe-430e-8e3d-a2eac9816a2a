import 'package:flutter/material.dart';

import '../../../../core/my_colors.dart';
import '../../../../core/my_styles.dart';

class ContainerMinusChicken extends StatelessWidget {
  final String day;
  final String chickenDie;
  final void Function() onTap;

  const ContainerMinusChicken({
    super.key,
    required this.day,
    required this.chickenDie,
    required this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.end,
      children: [
        Text(
          day,
          style: MyStyles.theMainTitleField.copyWith(
            fontWeight: FontWeight.w400,
          ),
        ),
        Container(
          width: double.infinity,
          height: 56,
          color: MyColors.primaryColor.withOpacity(0.5),
          child: Row(
            //   mainAxisAlignment: MainAxisAlignment.spaceEvenly,
            children: [
              Expanded(
                flex: 0,
                child: IconButton(
                  onPressed: onTap,
                  icon: const Icon(
                    Icons.delete,
                    color: Colors.red,
                  ),
                ),
              ),
              Expanded(
                  flex: 3,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      Text(
                        chickenDie,
                        style: MyStyles.theMainTitleField.copyWith(
                          color: Colors.black,
                        ),
                      ),
                      const Icon(
                        Icons.arrow_back,
                        color: Colors.black,
                      ),
                      Text(
                        'النافق',
                        style: MyStyles.theMainTitleField.copyWith(
                          color: Colors.black,
                        ),
                      ),
                    ],
                  ))
            ],
          ),
        ),
      ],
    );
  }
}
