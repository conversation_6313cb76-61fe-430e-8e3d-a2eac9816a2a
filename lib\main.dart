import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:hive_flutter/adapters.dart';
import 'package:mk2_dashboard/core/const.dart';
import 'package:mk2_dashboard/feature/checken_number/bloc/chicken_num_cubit.dart';
import 'package:mk2_dashboard/feature/checken_number/model/chicken_model.dart';
import 'package:mk2_dashboard/feature/home/<USER>';
import 'package:mk2_dashboard/feature/invoice/bloc/invoice_cubit.dart';
import 'package:mk2_dashboard/feature/invoice/model/invoice.dart';

void main() async {
  WidgetsFlutterBinding.ensureInitialized();
  await Hive.initFlutter();
  Hive.registerAdapter(InvoiceAdapter());
  await Hive.openBox<Invoice>('invoicesxxx');
  await Hive.openBox<Invoice>('invoicesModel1xx');
  await Hive.openBox<Invoice>('invoicesModel2xx');
  await Hive.openBox<Invoice>('invoicesModel3xx');
  await Hive.openBox<Invoice>('invoicesModel4xx');
  await Hive.openBox<Invoice>('invoicesModel5xx');
  await Hive.openBox<Invoice>('invoicesModel6xx');
  await Hive.openBox<Invoice>('invoicesModel7xx');
  await Hive.openBox<Invoice>('invoicesModel8xx');
  await Hive.openBox<Invoice>('invoicesModel9xx');
  await Hive.openBox<Invoice>('invoicesModel10xx');
  await Hive.openBox<Invoice>('invoicesModel11xx');
  await Hive.openBox<Invoice>('invoicesModel12xx');
  await Hive.openBox<Invoice>('invoicesModel13xx');
  await Hive.openBox<Invoice>('invoicesModel14xx');
  await Hive.openBox<Invoice>('invoicesModel15xx');
  await Hive.openBox<Invoice>('invoicesModel16xx');
  await Hive.openBox<Invoice>('invoicesModel17xx');
  await Hive.openBox<Invoice>('invoicesModel18xx');
  await Hive.openBox<Invoice>('invoicesModel19xx');
  await Hive.openBox<Invoice>('invoicesModel20xx');
  await Hive.openBox<Invoice>('invoicesModel21xx');
  await Hive.openBox<Invoice>('invoicesModel22xx');
  await Hive.openBox<Invoice>('invoicesModel23xx');
  await Hive.openBox<Invoice>('invoicesModel24xx');
  Hive.registerAdapter(ChickenModelAdapter());
  await Hive.openBox<ChickenModel>(kChickenBox);
  await Hive.openBox('totalNum');
  await Hive.openBox('minusNum');

  runApp(const MyApp());
}

class MyApp extends StatelessWidget {
  const MyApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MultiBlocProvider(
      providers: [
        BlocProvider(
          create: (context) => ChickenNumberCubit()..fetchTicketDies(),
        ),
        BlocProvider(
          create:
              (context) =>
                  AppInvoceCubit()
                    ..fetchInvoicemodel()
                    ..fetchInvoicemodel1()
                    ..fetchInvoicemodel2()
                    ..fetchInvoicemodel3()
                    ..fetchInvoicemodel4()
                    ..fetchInvoicemodel5()
                    ..fetchInvoicemodel6()
                    ..fetchInvoicemodel7()
                    ..fetchInvoicemodel8()
                    ..fetchInvoicemodel9()
                    ..fetchInvoicemodel10()
                    ..fetchInvoicemodel11()
                    ..fetchInvoicemodel12()
                    ..fetchInvoicemodel13()
                    ..fetchInvoicemodel14()
                    ..fetchInvoicemodel15()
                    ..fetchInvoicemodel16()
                    ..fetchInvoicemodel17()
                    ..fetchInvoicemodel18()
                    ..fetchInvoicemodel19()
                    ..fetchInvoicemodel20()
                    ..fetchInvoicemodel21()
                    ..fetchInvoicemodel22()
                    ..fetchInvoicemodel23()
                    ..fetchInvoicemodel24(),
        ),
      ],
      child: MaterialApp(
        title: 'Flutter Demo',
        debugShowCheckedModeBanner: false,
        theme: ThemeData(
          scaffoldBackgroundColor: Colors.white,
          //   colorScheme: ColorScheme.fromSeed(seedColor: Colors.deepPurple),
          useMaterial3: true,
        ),
        home: const HomeScreen(),
      ),
    );
  }
}
