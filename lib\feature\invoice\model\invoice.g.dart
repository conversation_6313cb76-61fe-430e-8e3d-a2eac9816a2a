// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'invoice.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class InvoiceAdapter extends TypeAdapter<Invoice> {
  @override
  final int typeId = 15;

  @override
  Invoice read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return Invoice(
      eggPrice: fields[0] as double,
      wastePrice: fields[1] as double,
      feedPrice: fields[2] as double,
      cartoonPrice: fields[3] as double,
      farmaPrice: fields[4] as double,
      employeePrice: fields[5] as double,
      anythingAnother: fields[6] as double,
      date: fields[7] as String,
      redPrice: fields[8] as double,
      greenPrice: fields[9] as double,
      totalPrice: fields[10] as double,
      prokeEggprice: fields[11] as double,
    );
  }

  @override
  void write(BinaryWriter writer, Invoice obj) {
    writer
      ..writeByte(12)
      ..writeByte(0)
      ..write(obj.eggPrice)
      ..writeByte(1)
      ..write(obj.wastePrice)
      ..writeByte(2)
      ..write(obj.feedPrice)
      ..writeByte(3)
      ..write(obj.cartoonPrice)
      ..writeByte(4)
      ..write(obj.farmaPrice)
      ..writeByte(5)
      ..write(obj.employeePrice)
      ..writeByte(6)
      ..write(obj.anythingAnother)
      ..writeByte(7)
      ..write(obj.date)
      ..writeByte(8)
      ..write(obj.redPrice)
      ..writeByte(9)
      ..write(obj.greenPrice)
      ..writeByte(10)
      ..write(obj.totalPrice)
      ..writeByte(11)
      ..write(obj.prokeEggprice);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is InvoiceAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
