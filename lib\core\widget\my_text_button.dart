import 'package:flutter/material.dart';
import 'package:mk2_dashboard/core/my_colors.dart';

class MyTextButton extends StatelessWidget {
  final BuildContext context;
  final Function()? onTap;
  final Widget widget;

  const MyTextButton({
    super.key,
    required this.context,
    required this.onTap,
    required this.widget,
  });

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: MediaQuery.of(context).size.width,
        height: 64,
        decoration: BoxDecoration(
          color: const Color.fromARGB(76, 0, 51, 102),
          borderRadius: BorderRadius.circular(16),
        ),
        child: Center(child: widget),
      ),
    );
  }
}
