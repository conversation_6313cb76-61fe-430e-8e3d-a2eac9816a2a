import 'package:hive/hive.dart';

part 'invoice.g.dart';

@HiveType(typeId: 15)
class Invoice extends HiveObject {
  @HiveField(0)
  final double eggPrice;
  @HiveField(1)
  final double wastePrice;
  @HiveField(2)
  final double feedPrice;
  @HiveField(3)
  final double cartoonPrice;
  @HiveField(4)
  final double farmaPrice;
  @HiveField(5)
  final double employeePrice;
  @HiveField(6)
  final double anythingAnother;
  @HiveField(7)
  final String date;
  @HiveField(8)
  final double redPrice;
  @HiveField(9)
  final double greenPrice;
  @HiveField(10)
  final double totalPrice;
  @HiveField(11)
  final double prokeEggprice;

  Invoice({
    required this.eggPrice,
    required this.wastePrice,
    required this.feedPrice,
    required this.cartoonPrice,
    required this.farmaPrice,
    required this.employeePrice,
    required this.anythingAnother,
    required this.date,
    required this.redPrice,
    required this.greenPrice,
    required this.totalPrice,
    required this.prokeEggprice,
  });
}
