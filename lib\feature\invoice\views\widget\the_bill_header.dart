import 'package:flutter/material.dart';

import '../../../../core/my_colors.dart';
import '../../../../core/my_styles.dart';

class TheBillHeader extends StatelessWidget {
  const TheBillHeader({super.key});

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          flex: 1,
          child: Column(
            children: [
              Text(
                '<EMAIL>',
                style: MyStyles.theMainTitleField
                    .copyWith(fontSize: 10, color: Colors.black),
              ),
              Text(
                'العدوه ,ترعه العدويه',
                style: MyStyles.theMainTitleField
                    .copyWith(fontSize: 10, color: Colors.black),
              ),
              Text(
                '+201284975580',
                style: MyStyles.theMainTitleField
                    .copyWith(fontSize: 10, color: Colors.black),
              ),
            ],
          ),
        ),
        Expanded(
          flex: 2,
          child: Container(
            color: MyColors.primaryColor,
            height: 80,
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 20),
              child: <PERSON>gn(
                alignment: Alignment.centerRight,
                child: Text(
                  'الفاتوره',
                  style: MyStyles.theMainTitleField.copyWith(
                    fontSize: 45,
                  ),
                ),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
