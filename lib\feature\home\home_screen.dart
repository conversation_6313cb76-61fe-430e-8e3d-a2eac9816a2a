import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:mk2_dashboard/core/spacer.dart';
import 'package:mk2_dashboard/feature/home/<USER>';
import 'package:mk2_dashboard/feature/home/<USER>/container_home.dart';

import '../checken_number/view/checken_number.dart';
import '../invoice/views/invoice_screen.dart';

class HomeScreen extends StatelessWidget {
  const HomeScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: Column(
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          const Header(),
          verticalSpacer(height: 40),
          Expanded(
            child: Padding(
              padding: const EdgeInsets.symmetric(horizontal: 10),
              child: GridView.count(
                crossAxisCount: 2,
                crossAxisSpacing: 10,
                mainAxisSpacing: 30,
                children: [
                  ContainerHome(
                    icon: Icons.receipt_long,
                    text: 'الفواتير',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (ctx) => const InvoiceScreen(),
                        ),
                      );
                    },
                  ),

                  ContainerHome(
                    icon: Icons.pets,
                    text: 'عدد الدجاج',
                    onTap: () {
                      Navigator.push(
                        context,
                        MaterialPageRoute(
                          builder: (ctx) => const ChickenNumberScreen(),
                        ),
                      );
                    },
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}
