// GENERATED CODE - DO NOT MODIFY BY HAND

part of 'chicken_model.dart';

// **************************************************************************
// TypeAdapterGenerator
// **************************************************************************

class ChickenModelAdapter extends TypeAdapter<ChickenModel> {
  @override
  final int typeId = 0;

  @override
  ChickenModel read(BinaryReader reader) {
    final numOfFields = reader.readByte();
    final fields = <int, dynamic>{
      for (int i = 0; i < numOfFields; i++) reader.readByte(): reader.read(),
    };
    return ChickenModel(
      total: fields[0] as int,
      chichenDie: fields[1] as int,
      date: fields[2] as String,
    );
  }

  @override
  void write(BinaryWriter writer, ChickenModel obj) {
    writer
      ..writeByte(3)
      ..writeByte(0)
      ..write(obj.total)
      ..writeByte(1)
      ..write(obj.chichenDie)
      ..writeByte(2)
      ..write(obj.date);
  }

  @override
  int get hashCode => typeId.hashCode;

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is ChickenModelAdapter &&
          runtimeType == other.runtimeType &&
          typeId == other.typeId;
}
