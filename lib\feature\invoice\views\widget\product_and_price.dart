import 'package:flutter/material.dart';

import '../../../../core/my_styles.dart';

class ProductAndPrice extends StatelessWidget {
  final String productName;
  final String productPrice;
  final bool isred;
  final bool isgreen;
  //final bool isGood;
  const ProductAndPrice({
    super.key,
    required this.productName,
    this.isred = false,
    this.isgreen = false,
    required this.productPrice,
  });

  @override
  Widget build(BuildContext context) {
    return Row(
      children: [
        Expanded(
          child: Text(
            productPrice,
            style: MyStyles.theMainTitleField.copyWith(
              fontSize: 17,
              color: isred == true
                  ? Colors.red
                  : isgreen == true
                      ? Colors.green
                      : Colors.black,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
        Text(
          productName,
          style: MyStyles.theMainTitleField.copyWith(
            fontSize: 17,
            color: isred == true
                ? Colors.red
                : isgreen == true
                    ? Colors.green
                    : Colors.black,
          ),
        ),
      ],
    );
  }
}
