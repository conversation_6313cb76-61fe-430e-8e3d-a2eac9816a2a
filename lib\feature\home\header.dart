import 'package:flutter/material.dart';
import 'package:mk2_dashboard/core/my_colors.dart';
import 'package:mk2_dashboard/core/my_image.dart';

class Header extends StatelessWidget {
  const Header({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      width: MediaQuery.of(context).size.width,
      height: 200,
      decoration: const BoxDecoration(
        color: MyColors.darkColor,
        borderRadius: BorderRadius.only(bottomRight: Radius.circular(100)),
      ),
      child: const Padding(
        padding: EdgeInsets.symmetric(horizontal: 20),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          children: [
            CircleAvatar(radius: 30, backgroundImage: AssetImage(MyImage.logo)),
            Text(
              '! مرحبا بك إسلام عواد',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
